{"private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "dev-ssl": "NODE_TLS_REJECT_UNAUTHORIZED=0 nuxt dev --https --ssl-cert localhost.pem --ssl-key localhost-key.pem", "generate": "nuxt generate", "preview": "nuxt preview", "stackblitz": "npm run dev"}, "stackblitz": {"startCommand": "npm run stackblitz"}, "devDependencies": {"@nuxtjs/tailwindcss": "^6.11.4", "@storyblok/nuxt": "^6.0.4", "nuxt": "^3.10.2"}}
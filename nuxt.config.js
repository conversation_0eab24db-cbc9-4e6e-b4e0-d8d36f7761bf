export default defineNuxtConfig({
  css: ['@/assets/css/roboto.css'],
  modules: [
    [
      '@storyblok/nuxt',
      {
        accessToken: '************************',
        apiOptions: {
          region: '' // Set 'US" if your space is created in US region (EU default)
        }
      },
    ],
    '@nuxtjs/tailwindcss',
  ],
  // Configure security headers to allow Storyblok Visual Editor
  nitro: {
    routeRules: {
      '/**': {
        headers: {
          'X-Frame-Options': 'ALLOWALL'
        }
      }
    }
  },
  // Alternative approach using ssr context
  ssr: true,
  // Configure app head for CSP if needed
  app: {
    head: {
      meta: [
        {
          'http-equiv': 'Content-Security-Policy',
          content: "frame-ancestors 'self' https://app.storyblok.com https://*.storyblok.com;"
        }
      ]
    }
  }
})

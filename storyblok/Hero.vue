<template>
  <section v-editable="blok" class="bg-white bg-center bg-cover relative lg:grid lg:h-[70vh] lg:place-content-center" :style="{backgroundImage: 'url(\'' + blok.background_image.filename + '\')'}">
    <div class="absolute inset-0 bg-black/25"/>
    <div class="mx-auto w-screen max-w-screen-xl px-4 py-16 sm:px-6 sm:py-24 lg:px-8 lg:py-32">
      <div class="mx-auto max-w-prose text-center text-white relative">
        <h1 class="text-4xl font-bold sm:text-5xl">
          {{ blok.title }}
        </h1>

        <p v-if="blok.subtitle" class="mt-4 text-base text-pretty sm:text-lg/relaxed">
          {{ blok.subtitle }}
        </p>

        <div class="mt-4 flex justify-center gap-4 sm:mt-6" v-if="blok.button_label">
          <a
              class="inline-block rounded border border-indigo-600 bg-indigo-600 px-5 py-3 font-medium text-white shadow-sm transition-colors hover:bg-indigo-700"
              :href="blok.button_link.url"
          >
            {{ blok.button_label }}
          </a>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
defineProps({ blok: Object })
</script>
